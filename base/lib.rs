/*!
Base module containing utility classes and configuration for the Eterna project.

This module provides Rust equivalents of the Python utility classes used for
database configuration, log parsing, and data management across various
network security tools and services.
*/

pub mod utils_classes;
pub mod utils_constants;
pub mod country_codes;
pub mod windows_server_audit_events;
pub mod snort_classifications;

// New utility modules converted from Python
pub mod utils;
pub mod utils_database;
pub mod utils_extra;
pub mod utils_htmx;
pub mod utils_ip;
pub mod utils_parsers;
pub mod utils_patterns;
pub mod utils_rahavard;
pub mod utils_tor;
pub mod utils_views;

// Re-export the main configuration structs for easier access
pub use utils_classes::{
    MysqlConfig, GeoLocationConfig, MaliciousConfig, DaemonConfig, DhcpConfig,
    DnsConfig, FilterLogConfig, RouterConfig, RouterBoardConfig, SnortConfig,
    SquidConfig, WindowsServerConfig,
};

// Re-export constants
pub use utils_classes::{HOUR_KEYS, EVENT_TYPES, EVENT_TYPES_CRITICALS, EVENT_TYPES_WARNINGS};
pub use utils_constants::{
    PICK_AN_OPTION, ACTION_ON_ERROR, MAX_KEY_LENGTH, MIN_LOG_SIZE, PRINT_ENDPOINT,
    CACHE_LIFE_SPAN, LRU_CACHE_MAXSIZE, BINARY_PATHS, ON_TRUE, LOGICAL_OPERATORS,
    SEARCH_SIGNS, LIMITS, TOPS_TO_SHOW, REFRESHES, LAST_LINES, RECENTS_TO_SHOW,
    TIMEOUTS, MAX_TRIES, SEASONS_LIST, MONTHS_LIST, HTTP_HEADERS, LIVE_MONITOR_DB_HEADERS,
    BinaryPaths, LogicalOperators, SearchSigns, Limits as LimitsStruct, TopsToShow,
    Refreshes as RefreshesStruct, LastLines as LastLinesStruct, RecentsToShow,
    Timeouts, MaxTries,
};

// Re-export data
pub use country_codes::COUNTRY_CODES_DICT;
pub use windows_server_audit_events::{WINDOWS_SERVER_AUDIT_EVENTS, get_category_from_event_id};
pub use snort_classifications::{
    CLASSIFICATIONS_DICT, CLASSIFICATIONS_CRITICALS, CLASSIFICATIONS_WARNINGS,
    CLASSIFICATIONS_LOWS, CLASSIFICATIONS_VERY_LOWS,
};

// Re-export utility functions
pub use utils::{
    paginate_vec, paginate_map, all_values_are_0, trim_keys, move_n_days, get_today_ymd,
    aggregate_values_of_dicts, quote_domain, unquote_domain, create_date_range,
    pick_middle_item_of_list, strip_protocol_and_path_from_url, normalize_dns_question_name,
    normalize_date, normalize_time, normalize_windowsserver_category, get_parsed_dirs,
    is_ymd, list_of_tuples_to_list, get_date_of_source_log, get_size_of_source_log,
    ymd_to_ym, dash_to_underscore, underscore_to_dash, break_name_of_database,
    create_name_of_database, get_rts_dts_dets, read_statistics_file, get_last_n_lines,
    humanize_bytes,
};

pub use utils_ip::{
    is_cidr, is_ip, is_ip_v4, is_ip_v6, is_private, is_private_v4, is_private_v6,
    get_hosts, is_mac_address, normalize_mac_address,
};

pub use utils_database::{
    DatabaseConfig, get_indexes_of_table, get_all_ips, get_all_domains, database_exists,
    table_exists, get_max_id, get_databases, get_databases_and_sizes, get_size_of_database,
    get_tables, get_tables_and_sizes, get_size_of_table,
};

pub use utils_extra::{
    VpnServer, StaticIp, FirewallRule, Interface, Sensor, BinaryPaths,
    VpnServerRepository, StaticIpRepository, FirewallRuleRepository, InterfaceRepository,
    SensorRepository, get_vpnserver_object, get_real_name, get_mac_address,
    get_ip_from_mac_address, get_firewall_rule_name, get_computer_name, get_interface,
    get_ip_of_chosen_sensor_name, get_interfaces_dict,
};

pub use utils_htmx::{
    HtmxResponse, convert_byte, convert_millisecond, get_computer_name_from_mac_address,
    get_computer_name_or_flag, get_real_name_htmx, get_firewall_rule_name_htmx,
    get_interface_htmx, comes_from_htmx, create_id_for_htmx_indicator, clear_messages,
};

pub use utils_parsers::{
    ParsedLogEntry, LogType, LogData, parse_ln, is_invalid_ln, invalid_line_sections,
    extract_dhcp_info, extract_dns_info,
};

pub use utils_views::{
    RequestParams, ActivityResponse, VisitsResponse, ViewContext, detailed_activity,
    overall_activity, visits,
};

pub use utils_tor::{
    TorConfig, TorCheckResponse, TorCheckResult, TorManager, check_is_tor, renew_tor_identity,
    renew_tor_identity_pkill, is_tor_running, get_tor_circuit_info,
};

pub use utils_rahavard::{
    ADMIN_LIST_DISPLAY_LINKS, ADMIN_READONLY_FIELDS, ADMIN_LIST_FILTER,
    ADMIN_USER_READONLY_FIELDS, ADMIN_USER_LIST_FILTER, JALALI_FORMAT,
    YMD_REGEX, HMS_REGEX, INT_OR_FLOAT_PATTERN, SIZE_SUFFIXES,
    contains_ymd, is_ymd, starts_with_ymdhms, calculate_offset,
    convert_byte, convert_millisecond, convert_second, convert_string_true_false_none_0,
    ConvertedValue, create_short_uuid, create_id_for_htmx_indicator,
    get_list_of_files, get_percent, is_numeric, is_int_or_float,
    persianize_number, sort_dict, to_tilda, intcomma_persian,
    convert_to_second, get_command, is_allowed, ColorMode, colorize, save_log,
};

pub use utils_patterns::{
    PatternType, get_pattern, match_log_line, NSLOOKUP_NAME_PATTERN, DAEMON_PATTERN,
    FILTERLOG_PATTERN, SNORT_PATTERN, SQUID_PATTERN, USERAUDIT_PATTERN, USERNOTICE_PATTERN,
    USERWARNING_PATTERN, ROUTER_PATTERN, ROUTERBOARD_PATTERN, SWITCH_PATTERN, VMWARE_PATTERN,
    WINDOWSSERVER_PATTERN, WS_AN_AD_PATTERN, WS_SW_PATTERN, VPNSERVER_PATTERN, DHCP_PATTERN,
    DNS_PATTERN, DNS_REST_PATTERN,
};
