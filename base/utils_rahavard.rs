/*!
<PERSON><PERSON>vard utility functions.

This module provides Rust equivalents of the Python utility functions
for date/time handling, string manipulation, file operations, and various
helper functions used throughout the Eterna project.
*/

use std::collections::HashMap;
use std::fs;
use std::path::Path;
use regex::Regex;
use chrono::{DateTime, Utc, NaiveDateTime};
use uuid::Uuid;
use once_cell::sync::Lazy;
use natural_sort::natural_sort;

/// Admin configuration constants
pub const ADMIN_LIST_DISPLAY_LINKS: &[&str] = &["id", "short_uuid"];
pub const ADMIN_READONLY_FIELDS: &[&str] = &["id", "short_uuid", "created", "updated"];
pub const ADMIN_LIST_FILTER: &[&str] = &["active", "short_uuid"];
pub const ADMIN_USER_READONLY_FIELDS: &[&str] = &["id", "short_uuid", "date_joined", "last_login"];
pub const ADMIN_USER_LIST_FILTER: &[&str] = &["is_superuser", "is_staff", "is_active", "is_limited_admin", "short_uuid"];

/// Jalali date format string
pub const JALALI_FORMAT: &str = "%A %H %M %S %d %m %Y";

/// Regular expressions for date and time patterns
pub static YMD_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"[0-9]{4}-[0-9]{2}-[0-9]{2}").unwrap());
pub static HMS_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"[0-9]{2}:[0-9]{2}:[0-9]{2}").unwrap());
pub static INT_OR_FLOAT_PATTERN: Lazy<Regex> = Lazy::new(|| Regex::new(r"^[0-9\.]+$").unwrap());

/// Size suffixes for byte conversion
pub static SIZE_SUFFIXES: Lazy<HashMap<&'static str, Vec<&'static str>>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("persian", vec![
        "بایت",
        "کیلوبایت", 
        "مگابایت",
        "گیگابایت",
        "ترابایت",
        "پتابایت",
        "اگزابایت",
        "زتابایت",
        "یوتابایت",
    ]);
    map.insert("latin", vec![
        "B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB",
    ]);
    map
});

/// Check if a string contains a date in the format YYYY-MM-DD
pub fn contains_ymd(string: &str) -> bool {
    YMD_REGEX.is_match(string)
}

/// Check if a given string matches the Year-Month-Day (YMD) format exactly
pub fn is_ymd(string: &str) -> bool {
    let full_pattern = Regex::new(r"^[0-9]{4}-[0-9]{2}-[0-9]{2}$").unwrap();
    full_pattern.is_match(string)
}

/// Check if a string starts with a date and time in the format 'YYYY-MM-DD HH:MM:SS'
pub fn starts_with_ymdhms(string: &str) -> bool {
    let pattern = Regex::new(r"^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2} ").unwrap();
    pattern.is_match(string)
}

/// Calculate the offset for a given page number and limit to show in a MySQL query
pub fn calculate_offset(page_number: u32, limit_to_show: u32) -> u32 {
    (page_number - 1) * limit_to_show
}

/// Convert a size in bytes to a human-readable string format
pub fn convert_byte(size_in_bytes: f64, to_persian: bool) -> String {
    if !is_numeric(size_in_bytes) || size_in_bytes == 0.0 {
        return if to_persian {
            "۰ بایت".to_string()
        } else {
            "0B".to_string()
        };
    }

    let i = (size_in_bytes.log2() / 10.0).floor() as usize;
    let p = 1024_f64.powi(i as i32);
    let mut conv = size_in_bytes / p;
    
    // Format to 1 decimal place
    conv = (conv * 10.0).round() / 10.0;
    
    let suffixes = if to_persian {
        &SIZE_SUFFIXES["persian"]
    } else {
        &SIZE_SUFFIXES["latin"]
    };

    let conv_str = if conv.fract() == 0.0 {
        format!("{}", conv as i32)
    } else {
        format!("{:.1}", conv)
    };

    if to_persian {
        format!("{} {}", persianize_number(&conv_str), suffixes[i])
    } else {
        format!("{}{}", conv_str, suffixes[i])
    }
}

/// Convert milliseconds to seconds
pub fn convert_millisecond(ms: f64, verbose: bool) -> String {
    let seconds = if is_numeric(ms) { ms / 1000.0 } else { 0.0 };
    convert_second(seconds, verbose)
}

/// Convert a given number of seconds into a human-readable string format
pub fn convert_second(seconds: f64, verbose: bool) -> String {
    if !is_numeric(seconds) {
        return if verbose { "0".to_string() } else { "0:00".to_string() };
    }

    if seconds == 0.0 {
        return if verbose { "0".to_string() } else { "0:00".to_string() };
    }

    if seconds < 1.0 {
        return if verbose { "~0".to_string() } else { "~0:00".to_string() };
    }

    let seconds = seconds as u64;
    let ss = seconds % 60;
    let mi = (seconds / 60) % 60;
    let hh = (seconds / 3600) % 24;
    let dd = (seconds / 3600 / 24) % 30;
    let mo = (seconds / 3600 / 24 / 30) % 12;
    let yy = seconds / 3600 / 24 / 30 / 12;

    if verbose {
        let mut parts = Vec::new();
        if yy > 0 { parts.push(format!("{} year{}", yy, if yy == 1 { "" } else { "s" })); }
        if mo > 0 { parts.push(format!("{} month{}", mo, if mo == 1 { "" } else { "s" })); }
        if dd > 0 { parts.push(format!("{} day{}", dd, if dd == 1 { "" } else { "s" })); }
        if hh > 0 { parts.push(format!("{} hr{}", hh, if hh == 1 { "" } else { "s" })); }
        if mi > 0 { parts.push(format!("{} min{}", mi, if mi == 1 { "" } else { "s" })); }
        if ss > 0 { parts.push(format!("{} sec{}", ss, if ss == 1 { "" } else { "s" })); }

        match parts.len() {
            0 => "0".to_string(),
            1 => parts[0].clone(),
            2 => format!("{} and {}", parts[0], parts[1]),
            _ => {
                let last = parts.pop().unwrap();
                format!("{} and {}", parts.join(", "), last)
            }
        }
    } else {
        if yy == 0 && mo == 0 && dd == 0 {
            format!("{:02}:{:02}:{:02}", hh, mi, ss)
        } else if yy == 0 && mo == 0 {
            format!("{:02}:{:02}:{:02}:{:02}", dd, hh, mi, ss)
        } else if yy == 0 {
            format!("{:02}:{:02}:{:02}:{:02}:{:02}", mo, dd, hh, mi, ss)
        } else {
            format!("{:02}:{:02}:{:02}:{:02}:{:02}:{:02}", yy, mo, dd, hh, mi, ss)
        }
    }
}

/// Convert specific string representations to their corresponding values
pub fn convert_string_true_false_none_0(item: &str) -> ConvertedValue {
    match item {
        "True" => ConvertedValue::Bool(true),
        "False" => ConvertedValue::Bool(false),
        "None" => ConvertedValue::None,
        "0" => ConvertedValue::Int(0),
        _ => ConvertedValue::String(item.to_string()),
    }
}

/// Enum for converted string values
#[derive(Debug, Clone, PartialEq)]
pub enum ConvertedValue {
    Bool(bool),
    Int(i32),
    None,
    String(String),
}

/// Generate a short UUID string
pub fn create_short_uuid() -> String {
    let uuid = Uuid::new_v4();
    format!("{:x}", uuid.as_u128() & 0xFFFFFFFF)
}

/// Generate a unique ID for an HTMX indicator
pub fn create_id_for_htmx_indicator(args: &[&str]) -> String {
    let joined = args.join("-");
    let with_suffix = format!("{}--htmx-indicator", joined);
    // Replace 3 or more consecutive hyphens with double hyphens
    let re = Regex::new(r"-{3,}").unwrap();
    re.replace_all(&with_suffix, "--").to_string()
}

/// Get a list of files in a directory with a specific extension, sorted naturally
pub fn get_list_of_files(directory: &str, extension: &str) -> Vec<String> {
    let path = Path::new(directory);
    if !path.exists() {
        return Vec::new();
    }

    let mut files = Vec::new();
    if let Ok(entries) = fs::read_dir(path) {
        for entry in entries.flatten() {
            if let Ok(file_type) = entry.file_type() {
                if file_type.is_file() {
                    if let Some(file_name) = entry.file_name().to_str() {
                        if file_name.ends_with(&format!(".{}", extension)) {
                            if let Some(full_path) = entry.path().to_str() {
                                files.push(full_path.to_string());
                            }
                        }
                    }
                }
            }
        }
    }

    // Natural sort
    files.sort_by(|a, b| natural_sort::compare(a, b));
    files
}

/// Calculate the percentage of a smaller number relative to a total number
pub fn get_percent(smaller_number: f64, total_number: f64, to_persian: bool) -> String {
    if smaller_number == 0.0 || total_number == 0.0 {
        return if to_persian { "۰".to_string() } else { "0".to_string() };
    }

    let perc = (smaller_number * 100.0) / total_number;
    
    if perc < 1.0 {
        return if to_persian { "~۰".to_string() } else { "~0".to_string() };
    }

    let perc = (perc * 10.0).round() / 10.0;
    let perc_str = if perc.fract() == 0.0 {
        format!("{}", perc as i32)
    } else {
        format!("{:.1}", perc)
    };

    if to_persian {
        persianize_number(&perc_str)
    } else {
        perc_str
    }
}

/// Check if the given value represents a number
pub fn is_numeric(value: f64) -> bool {
    value.is_finite()
}

/// Check if the given string represents an integer or a float
pub fn is_int_or_float(string: &str) -> bool {
    INT_OR_FLOAT_PATTERN.is_match(string)
}

/// Convert an English number to its Persian equivalent (placeholder)
pub fn persianize_number(number: &str) -> String {
    // This is a simplified version - in a real implementation you'd want
    // to use a proper Persian number conversion library
    number
        .replace('0', "۰")
        .replace('1', "۱")
        .replace('2', "۲")
        .replace('3', "۳")
        .replace('4', "۴")
        .replace('5', "۵")
        .replace('6', "۶")
        .replace('7', "۷")
        .replace('8', "۸")
        .replace('9', "۹")
}

/// Sort a dictionary based on its keys or values
pub fn sort_dict<K, V>(dictionary: HashMap<K, V>, based_on: &str, reverse: bool) -> Vec<(K, V)>
where
    K: Clone + Ord,
    V: Clone + Ord,
{
    let mut items: Vec<(K, V)> = dictionary.into_iter().collect();
    
    match based_on {
        "key" => items.sort_by(|a, b| if reverse { b.0.cmp(&a.0) } else { a.0.cmp(&b.0) }),
        "value" => items.sort_by(|a, b| if reverse { b.1.cmp(&a.1) } else { a.1.cmp(&b.1) }),
        _ => {}
    }
    
    items
}

/// Replace the home directory path in the given text with a tilde (~)
pub fn to_tilda(text: &str) -> String {
    if let Ok(home) = std::env::var("HOME") {
        text.replace(&home, "~")
    } else {
        text.to_string()
    }
}

/// Formats a Persian number string by adding commas as thousand separators
pub fn intcomma_persian(num: &str) -> String {
    let mut commad = String::new();
    let mut is_float = false;
    let mut separator = "";
    let mut right = "";

    // Check if it's a float with . or /
    let float_dot_re = Regex::new(r"^[۱۲۳۴۵۶۷۸۹۰]+\.[۱۲۳۴۵۶۷۸۹۰]+$").unwrap();
    let float_slash_re = Regex::new(r"^[۱۲۳۴۵۶۷۸۹۰]+\/[۱۲۳۴۵۶۷۸۹۰]+$").unwrap();

    let left = if float_dot_re.is_match(num) {
        let parts: Vec<&str> = num.split('.').collect();
        right = parts[1];
        separator = ".";
        is_float = true;
        parts[0]
    } else if float_slash_re.is_match(num) {
        let parts: Vec<&str> = num.split('/').collect();
        right = parts[1];
        separator = "/";
        is_float = true;
        parts[0]
    } else {
        num
    };

    for (idx, char) in left.chars().rev().enumerate() {
        if idx % 3 == 0 && idx > 0 {
            commad = format!("{}،{}", char, commad);
        } else {
            commad = format!("{}{}", char, commad);
        }
    }

    if is_float {
        format!("{}{}{}", commad, separator, right)
    } else {
        commad
    }
}

/// Convert a datetime object to seconds since the epoch
pub fn convert_to_second(date_obj: DateTime<Utc>) -> i64 {
    date_obj.timestamp()
}

/// Extract the command name from a given full path
pub fn get_command(full_path: &str, drop_extension: bool) -> String {
    let path = Path::new(full_path);
    let file_name = path.file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("");

    if drop_extension {
        if let Some(stem) = path.file_stem().and_then(|s| s.to_str()) {
            stem.to_string()
        } else {
            file_name.to_string()
        }
    } else {
        file_name.to_string()
    }
}

/// Check if a command is allowed based on inclusion and exclusion lists
pub fn is_allowed(cmd: &str, only: &[String], exclude: &[String]) -> bool {
    let mut allowed = true;

    if !only.is_empty() {
        allowed = false;
    }
    if !only.is_empty() && only.contains(&cmd.to_string()) {
        allowed = true;
    }
    if !exclude.is_empty() && exclude.contains(&cmd.to_string()) {
        allowed = false;
    }

    allowed
}

/// Color mode for terminal output
#[derive(Debug, Clone)]
pub enum ColorMode {
    AlreadyParsed,
    Command,
    CountryError,
    CountrySuccess,
    CountryWarning,
    Error,
    HostName,
    Invalid,
    Warning,
    Ymdhms,
    Dropping,
    Removing,
    Copying,
    Creating,
    AccomplishedIn,
    CompressedIn,
    Done,
    Dropped,
    FetchedIn,
    ParsedIn,
    Removed,
    Success,
    UpdatedIn,
    WroteIn,
    Default,
}

/// Simple colorize function (returns text as-is since we don't have Django styling)
pub fn colorize(mode: ColorMode, text: &str) -> String {
    // In a real implementation, you might want to use a crate like `colored`
    // for terminal colors, but keeping it simple here
    text.to_string()
}

/// Log a message with timestamp
pub fn save_log(command: &str, host_name: &str, dest_file: &str, msg: &str, echo: bool) -> Result<(), std::io::Error> {
    use std::fs::OpenOptions;
    use std::io::Write;

    let now = chrono::Utc::now();
    let ymdhms = now.format("%Y-%m-%d %H:%M:%S").to_string();
    let msg = to_tilda(msg);

    if echo {
        let colored_msg = if msg.contains("accomplished in") {
            colorize(ColorMode::AccomplishedIn, &msg)
        } else if msg.contains("wrote in") {
            colorize(ColorMode::WroteIn, &msg)
        } else if msg.contains("parsed in") {
            colorize(ColorMode::ParsedIn, &msg)
        } else if msg.contains("compressed in") {
            colorize(ColorMode::CompressedIn, &msg)
        } else if msg == "done" {
            colorize(ColorMode::Done, &msg)
        } else if msg.contains("fetched in") {
            colorize(ColorMode::FetchedIn, &msg)
        } else if msg.contains("updated in") {
            colorize(ColorMode::UpdatedIn, &msg)
        } else if msg.contains("ERROR") {
            colorize(ColorMode::Error, &msg)
        } else if msg.contains("WARNING") {
            colorize(ColorMode::Warning, &msg)
        } else if msg.contains("removing") {
            colorize(ColorMode::Removing, &msg)
        } else if msg.contains("creating") {
            colorize(ColorMode::Creating, &msg)
        } else if msg.contains("dropping") {
            colorize(ColorMode::Dropping, &msg)
        } else if msg.contains("copying") {
            colorize(ColorMode::Copying, &msg)
        } else {
            msg.clone()
        };

        println!("{} {} {} {}",
            colorize(ColorMode::HostName, host_name),
            colorize(ColorMode::Command, command),
            colorize(ColorMode::Ymdhms, &ymdhms),
            colored_msg
        );
    }

    let mut file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(dest_file)?;

    writeln!(file, "{} {}", ymdhms, msg)?;
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_contains_ymd() {
        assert!(contains_ymd("Today's date is 2023-10-05."));
        assert!(!contains_ymd("No date here!"));
        assert!(contains_ymd("The event is on 2023-12-25."));
        assert!(!contains_ymd("Date: 2023/10/05"));
    }

    #[test]
    fn test_is_ymd() {
        assert!(is_ymd("2023-10-05"));
        assert!(!is_ymd("05-10-2023"));
        assert!(!is_ymd("2023/10/05"));
        assert!(!is_ymd("20231005"));
    }

    #[test]
    fn test_calculate_offset() {
        assert_eq!(calculate_offset(1, 25), 0);
        assert_eq!(calculate_offset(2, 25), 25);
        assert_eq!(calculate_offset(3, 10), 20);
    }

    #[test]
    fn test_create_short_uuid() {
        let uuid1 = create_short_uuid();
        let uuid2 = create_short_uuid();
        assert_ne!(uuid1, uuid2);
        assert!(uuid1.len() <= 8);
    }

    #[test]
    fn test_convert_string_true_false_none_0() {
        assert_eq!(convert_string_true_false_none_0("True"), ConvertedValue::Bool(true));
        assert_eq!(convert_string_true_false_none_0("False"), ConvertedValue::Bool(false));
        assert_eq!(convert_string_true_false_none_0("None"), ConvertedValue::None);
        assert_eq!(convert_string_true_false_none_0("0"), ConvertedValue::Int(0));
        assert_eq!(convert_string_true_false_none_0("Hello"), ConvertedValue::String("Hello".to_string()));
    }

    #[test]
    fn test_get_percent() {
        assert_eq!(get_percent(25.0, 100.0, false), "25");
        assert_eq!(get_percent(0.0, 100.0, false), "0");
        assert_eq!(get_percent(25.0, 0.0, false), "0");
        assert_eq!(get_percent(1.0, 100.0, false), "1");
    }

    #[test]
    fn test_is_allowed() {
        assert!(is_allowed("test", &[], &[]));
        assert!(!is_allowed("test", &["other".to_string()], &[]));
        assert!(is_allowed("test", &["test".to_string()], &[]));
        assert!(!is_allowed("test", &[], &["test".to_string()]));
    }
}
